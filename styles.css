/* RTL and Arabic Support */
body {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    background-color: #f8f9fa;
    direction: rtl;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Login Container */
.login-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.login-container .card {
    border: none;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

/* Sidebar */
.sidebar {
    background-color: #343a40;
    min-height: calc(100vh - 56px);
    border-left: 1px solid #dee2e6;
}

.sidebar .list-group-item {
    background-color: transparent;
    border: none;
    color: #adb5bd;
    border-radius: 0;
}

.sidebar .list-group-item:hover {
    background-color: #495057;
    color: white;
}

.sidebar .list-group-item.active {
    background-color: #0d6efd;
    color: white;
}

/* Main Content */
.main-content {
    background-color: #f8f9fa;
    min-height: calc(100vh - 56px);
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* Product Grid */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.product-card {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
}

.product-card:hover {
    border-color: #0d6efd;
    transform: scale(1.05);
}

.product-card.selected {
    border-color: #0d6efd;
    background-color: #e7f3ff;
}

/* Shopping Cart */
.cart-section {
    background-color: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.cart-item:last-child {
    border-bottom: none;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    padding: 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Buttons */
.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    border: none;
}

/* Tables */
.table {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

/* Modal */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* Toast Notifications */
.toast {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.toast-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
}

/* Charts */
.chart-container {
    position: relative;
    height: 300px;
    background-color: white;
    border-radius: 10px;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 56px;
        left: -250px;
        width: 250px;
        height: calc(100vh - 56px);
        z-index: 1000;
        transition: left 0.3s;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Arabic Text Improvements */
.arabic-text {
    font-family: 'Amiri', 'Times New Roman', serif;
    line-height: 1.6;
}

/* Ice Cream Specific Styles */
.ice-cream-flavor {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    margin: 0.25rem;
    background-color: #e9ecef;
    border-radius: 15px;
    font-size: 0.875rem;
}

.ice-cream-flavor.vanilla { background-color: #fff3cd; }
.ice-cream-flavor.chocolate { background-color: #d4b8a3; color: white; }
.ice-cream-flavor.strawberry { background-color: #f8d7da; }
.ice-cream-flavor.mint { background-color: #d1e7dd; }

/* Status Badges */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
}

.status-in-stock { background-color: #d1e7dd; color: #0a3622; }
.status-low-stock { background-color: #fff3cd; color: #664d03; }
.status-out-of-stock { background-color: #f8d7da; color: #58151c; }

/* Currency Display */
.currency {
    font-weight: 600;
    color: #0d6efd;
}

.currency::after {
    content: " ر.ع";
    font-size: 0.875em;
    color: #6c757d;
}
