modules = ["nodejs-20"]

[nix]
channel = "stable-24_05"

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Ice Cream Shop Server"

[[workflows.workflow]]
name = "Ice Cream Shop Server"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm init -y && npm install express && node server.js"
waitForPort = 5000

[[ports]]
localPort = 5000
externalPort = 80
